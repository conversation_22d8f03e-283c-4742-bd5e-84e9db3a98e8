{"name": "silicon-based-teahouse", "version": "0.1.0", "private": true, "scripts": {"dev": "TURBOPACK=0 next dev", "build": "npm run copy-images && next build", "start": "next start", "lint": "next lint", "copy-images": "node scripts/copy-images.js"}, "dependencies": {"@next/mdx": "^15.3.3", "gray-matter": "^4.0.3", "html2canvas": "^1.4.1", "marked": "^15.0.12", "mermaid": "^11.6.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-html": "^16.0.1", "unified": "^11.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.52", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}