"use client";

import React, { useState } from "react";
import "@/styles/blogStyles.css";
import CoverGeneratorModal from './CoverGeneratorModal';

export interface ArticleMetadata {
  title: string;
  date: string;
  excerpt?: string;
}

interface ArticleLayoutProps {
  children: React.ReactNode;
  metadata?: ArticleMetadata;
}

export default function ArticleLayout({ children, metadata }: ArticleLayoutProps) {
  const [copySuccess, setCopySuccess] = useState("");
  const [showPhoneFrame, setShowPhoneFrame] = useState(true);
  const [showCoverModal, setShowCoverModal] = useState(false);

  // 处理图片路径，将本地路径转换为绝对路径
  const processImagePaths = (html: string): string => {
    return html.replace(
      /src="\/posts\//g,
      `src="${window.location.origin}/posts/`
    ).replace(
      /src="\/assets\//g,
      `src="${window.location.origin}/assets/`
    );
  };

  // 获取内联样式
  const getInlineStyles = (): string => {
    const styles = `
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif; font-size: 16px; line-height: 1.6; color: #333; background-color: #fff; padding: 0; margin: 0; word-wrap: break-word; word-break: break-all; }
        .article-content { font-size: 16px; line-height: 1.8; }
        h1 { font-size: 22px; font-weight: bold; color: #333; text-align: center; margin-bottom: 20px; line-height: 1.4; padding: 20px 10px; background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(255, 107, 179, 0.05) 100%); border: 1px solid rgba(0, 212, 255, 0.2); border-radius: 12px; }
        h2 { font-size: 20px; font-weight: bold; color: #333; margin: 30px 0 15px 0; padding: 12px 16px; border-bottom: 2px solid #00d4ff; border-left: 4px solid #ff6bb3; background: linear-gradient(90deg, rgba(255, 107, 179, 0.08) 0%, transparent 50%); border-radius: 0 8px 8px 0; }
        h3 { font-size: 18px; font-weight: bold; color: #333; margin: 25px 0 12px 0; padding: 8px 0 8px 16px; border-left: 4px solid #ff6bb3; background: linear-gradient(90deg, rgba(255, 107, 179, 0.05) 0%, transparent 30%); border-radius: 0 6px 6px 0; }
        h4 { font-size: 16px; font-weight: bold; color: #555; margin: 20px 0 10px 0; }
        p { margin-bottom: 16px; text-align: justify; color: #333; }
        ul, ol { margin: 16px 0; padding-left: 20px; }
        li { margin-bottom: 8px; line-height: 1.6; }
        strong { color: #ff6bb3; font-weight: bold; }
        a { color: #00d4ff; text-decoration: none; word-break: break-all; }
        img { max-width: 100%; height: auto; display: block; margin: 20px auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        pre { background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); border: 1px solid #00d4ff; border-radius: 8px; padding: 16px; margin: 16px 0; overflow-x: auto; font-size: 14px; line-height: 1.4; }
        code { font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace; background-color: #f5f5f5; padding: 2px 4px; border-radius: 3px; font-size: 14px; color: #ff6bb3; }
        pre code { background-color: transparent; padding: 0; color: #00ff88; display: block; }
        .divider { height: 2px; background: linear-gradient(90deg, transparent 0%, #00d4ff 20%, #ff6bb3 50%, #39ff14 80%, transparent 100%); margin: 40px 0; border: none; border-radius: 1px; }
        .highlight-box { background: linear-gradient(135deg, rgba(57, 255, 20, 0.08) 0%, rgba(57, 255, 20, 0.02) 100%); border-left: 4px solid #39ff14; border: 1px solid rgba(57, 255, 20, 0.3); padding: 16px 20px; margin: 20px 0; border-radius: 8px; }
        .highlight-box strong { color: #39ff14; }
        .section { margin-bottom: 30px; }
        .intro-text { font-size: 17px; color: #666; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(255, 107, 179, 0.05) 100%); border-radius: 12px; border: 1px solid rgba(0, 212, 255, 0.2); }
        .footer-note { text-align: center; font-size: 14px; color: #999; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; }
        .brand-header { text-align: center; margin-bottom: 30px; padding: 20px 0; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%); border-radius: 12px; }
        .brand-name { font-size: 24px; font-weight: bold; color: #ff6bb3; margin-bottom: 8px; }
        .brand-subtitle { font-size: 14px; color: #00d4ff; }
        blockquote { margin: 20px 0; padding: 15px 20px; border-left: 4px solid #00d4ff; background: linear-gradient(90deg, rgba(0, 212, 255, 0.05) 0%, transparent 50%); border-radius: 0 8px 8px 0; font-style: italic; color: #555; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f5f5f5; font-weight: bold; }
      </style>
    `;
    return styles;
  };

  // 复制带样式的内容
  const copyArticleWithStyles = async () => {
    try {
      const contentElement = document.querySelector(".article-outer-container");
      if (!contentElement) return;

      // 克隆元素以避免修改原始DOM
      const clonedElement = contentElement.cloneNode(true) as HTMLElement;

      // 处理图片路径
      const processedHTML = processImagePaths(clonedElement.outerHTML);

      // 创建完整的HTML文档，包含样式
      const styledHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          ${getInlineStyles()}
        </head>
        <body>
          ${processedHTML}
        </body>
        </html>
      `;

      // 尝试复制富文本格式
      if (navigator.clipboard && window.ClipboardItem) {
        const blob = new Blob([styledHTML], { type: 'text/html' });
        const clipboardItem = new ClipboardItem({
          'text/html': blob,
          'text/plain': new Blob([clonedElement.textContent || ''], { type: 'text/plain' })
        });

        await navigator.clipboard.write([clipboardItem]);
        setCopySuccess("✅ 富文本内容已复制！可直接粘贴到微信公众号");
      } else {
        // 降级到纯文本复制
        await navigator.clipboard.writeText(styledHTML);
        setCopySuccess("✅ HTML内容已复制！");
      }

      setTimeout(() => setCopySuccess(""), 3000);
    } catch (error) {
      console.error("复制失败:", error);
      setCopySuccess("❌ 复制失败，请重试");
      setTimeout(() => setCopySuccess(""), 2000);
    }
  };



  return (
    <div className="article-page">
      {/* 手机壳切换按钮 */}
      <button
        className="frame-toggle"
        onClick={() => setShowPhoneFrame(!showPhoneFrame)}
      >
        {showPhoneFrame ? "隐藏手机框架" : "显示手机框架"}
      </button>

      {/* 外部容器（会被复制，包含标题和复制按钮） */}
      <div className="article-outer-container">
        {/* 文章标题 */}

        {/* 左右布局容器 */}
        <div className="flex flex-col lg:flex-row gap-6">
          <div className="lg:w-1/2">
            <div className="sticky top-6">
              {metadata && (
                <h1 className="article-title">{metadata.title}</h1>
              )}

              {/* 复制按钮 */}
              <button className="copy-button" onClick={copyArticleWithStyles}>
                {copySuccess || "复制带样式的内容"}
              </button>

              {/* 封面生成器按钮 */}
              <button
                className="copy-button mt-2"
                onClick={() => setShowCoverModal(true)}
                style={{ backgroundColor: '#ff6bb3' }}
              >
                🎨 生成封面图片
              </button>

              {metadata && (
                <div className="border border-slate-700/50 rounded-xl p-6 mt-4">
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">
                    关于本文
                  </h3>
                  <p className="text-slate-300 mb-4">
                    这是一篇关于 {metadata.title} 的文章，发布于 {metadata.date}。
                  </p>
                  <div className="border-t border-slate-700/50 pt-4 mt-4">
                    <p className="text-slate-400 text-sm">
                      如需了解更多内容，请关注我们的微信公众号：
                    </p>
                    <p className="text-pink-400 font-bold mt-2">硅基茶馆2077</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 内部容器（手机壳） */}
          <div
            className={`article-container ${
              showPhoneFrame ? "phone-frame" : ""
            } lg:w-1/2`}
          >
            <div className="article-content">
              {children}
            </div>

            {/* 页脚品牌信息 */}
            <div className="footer-note">
              {/* 品牌头部（移至文章页脚） */}
              <div className="brand-header mt-8">
                <div className="brand-name">硅基茶馆2077</div>
                <div className="brand-subtitle">
                  Silicon Based Teahouse · Future Tech Insights
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 封面生成器弹窗 */}
      <CoverGeneratorModal
        isOpen={showCoverModal}
        onClose={() => setShowCoverModal(false)}
        initialTitle={metadata?.title || "文章标题"}
        initialSubtitle={metadata?.excerpt || "文章副标题"}
      />
    </div>
  );
}
