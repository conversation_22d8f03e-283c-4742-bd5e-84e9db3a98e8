@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

/* 只对没有 Tailwind 类的元素应用默认颜色 */
body:not([class*="text-"]):not([class*="bg-"]) {
  background: var(--background);
  color: var(--foreground);
}

/* 确保 Tailwind 类的优先级 */
[class*="text-"] {
  color: unset !important;
}

[class*="bg-"] {
  background: unset !important;
}
